#!/bin/sh
set -e

_terminate() {
  echo "Signal (SIGINT/SIGTERM) received. Waiting for gracefully shutdown..."
  kill $(jobs -p)
  wait
  exit 0
}

trap _terminate SIGINT SIGTERM

# Run migration
export DATABASE_URL="postgresql://$POSTGRES_USER:$POSTGRES_PASS@$POSTGRES_HOST:$POSTGRES_PORT/${POSTGRES_DB:-agent}?sslmode=disable"
echo "DATABASE_URL: $DATABASE_URL"

# Check if migrations directory exists and has files
if [ -d "migrations" ] && [ "$(ls -A migrations)" ]; then
    # Fix checksum error by re-hashing migration files
    echo "Re-hashing migration files to fix checksum errors..."
    atlas migrate hash --dir file://migrations

    # Check migration status first
    echo "Checking migration status..."
    if atlas migrate status --url $DATABASE_URL --dir file://migrations > /dev/null 2>&1; then
        echo "Migration status check passed, applying migrations..."
        atlas migrate apply --url $DATABASE_URL --dir file://migrations
    else
        echo "Database is not clean or migration status check failed."
        echo "Trying to apply migrations with --allow-dirty flag..."
        atlas migrate apply --url $DATABASE_URL --dir file://migrations --allow-dirty
    fi
else
    echo "No migration files found or migrations directory doesn't exist, skipping migrations..."
fi

# Wait for any process to exit
# wait -n

# Exit with status of process that exited first
exec $@
